using AutoMapper;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using System.Linq.Expressions;

namespace CoverGo.Payments.Application.Common.Handlers;

public abstract class AggregatesQueryHandler<TAggregate, TAggregatesQuery, TWhere>(
    IMapper mapper,
    IMongoCollection<TAggregate> mongoCollection)
    where TAggregate : AggregateRootBase<string>
    where TAggregatesQuery : PagedQuery<TWhere>
{
    protected static readonly FilterDefinitionBuilder<TAggregate> SBuilder = new();

    protected abstract FilterDefinition<TAggregate> GetFilter(TWhere? filter);

    protected virtual IEnumerable<SortDefinition<TAggregate>> GetSorts(TAggregatesQuery query) =>
        [Builders<TAggregate>.Sort.Descending(o => o.EntityAuditInfo.CreatedAt)];

    protected async Task<PagedResult<TDto>> Handle<TDto>(TAggregatesQuery request, CancellationToken cancellationToken)
    {
        FilterDefinition<TAggregate> mongoFilter = GetFilter(request.Where);

        IAggregateFluent<TAggregate>? aggregate = mongoCollection.Aggregate()
            .Match(mongoFilter);
        IEnumerable<SortDefinition<TAggregate>> sortDefinitions = GetSorts(request);
        PipelineDefinition<TAggregate, TAggregate>? docsFace = sortDefinitions.Aggregate(
            new EmptyPipelineDefinition<TAggregate>(),
            (PipelineDefinition<TAggregate, TAggregate> accumulator, SortDefinition<TAggregate> sort) =>
                accumulator.Sort(sort));

        int? skip = request.Skip;
        if (skip.HasValue)
        {
            docsFace = docsFace.Skip(skip.Value);
        }

        int? take = request.Take ?? AggregatesQueryValidator<TAggregatesQuery, TWhere>.MaxItems;

        docsFace = docsFace.Limit(take.Value);

        const string docs = "Docs";
        const string total = "Total";
        IDictionary<string, BsonDocument[]>? facet = await aggregate
            .Facet<TAggregate, IDictionary<string, BsonDocument[]>>(
                AggregateFacet.Create(docs, docsFace),
                AggregateFacet.Create(total, new EmptyPipelineDefinition<TAggregate>().Count())
            ).FirstOrDefaultAsync(cancellationToken);
        BsonDocument[] totalResultSet = facet[total];
        int totalCount = totalResultSet.Length != 0 ? totalResultSet[0]["count"].AsInt32 : 0;
        IEnumerable<TAggregate> list = facet[docs].Select(doc => BsonSerializer.Deserialize<TAggregate>(doc));
        return new PagedResult<TDto> { Items = mapper.Map<IEnumerable<TDto>>(list), TotalCount = totalCount };
    }

    protected async Task<PagedResult<TGroupDto>> HandleGroupBy<TGroupDto>(
        TAggregatesQuery request,
        BsonDocument groupByExpression,
        CancellationToken cancellationToken)
    {
        FilterDefinition<TAggregate> mongoFilter = GetFilter(request.Where);

        IAggregateFluent<TAggregate> aggregate = mongoCollection.Aggregate()
            .Match(mongoFilter);

        // Apply grouping
        IAggregateFluent<BsonDocument> groupedAggregate = aggregate.Group(groupByExpression);

        // Create pipeline for grouped results with sorting and pagination
        PipelineDefinition<BsonDocument, BsonDocument> docsFace = new EmptyPipelineDefinition<BsonDocument>();

        int? skip = request.Skip;
        if (skip.HasValue)
        {
            docsFace = docsFace.Skip(skip.Value);
        }

        int? take = request.Take ?? AggregatesQueryValidator<TAggregatesQuery, TWhere>.MaxItems;
        docsFace = docsFace.Limit(take.Value);

        const string docs = "Docs";
        const string total = "Total";

        IDictionary<string, BsonDocument[]> facet = await groupedAggregate
            .Facet<BsonDocument, IDictionary<string, BsonDocument[]>>(
                AggregateFacet.Create(docs, docsFace),
                AggregateFacet.Create(total, new EmptyPipelineDefinition<BsonDocument>().Count())
            ).FirstOrDefaultAsync(cancellationToken);

        BsonDocument[] totalResultSet = facet[total];
        int totalCount = totalResultSet.Length != 0 ? totalResultSet[0]["count"].AsInt32 : 0;

        IEnumerable<BsonDocument> groupedResults = facet[docs];
        return new PagedResult<TGroupDto>
        {
            Items = mapper.Map<IEnumerable<TGroupDto>>(groupedResults),
            TotalCount = totalCount
        };
    }

    protected async Task<PagedResult<TGroupDto>> HandleGroupBy<TGroupDto>(
        TAggregatesQuery request,
        Expression<Func<TAggregate, object>> groupByField,
        CancellationToken cancellationToken)
    {
        FilterDefinition<TAggregate> mongoFilter = GetFilter(request.Where);

        IAggregateFluent<TAggregate> aggregate = mongoCollection.Aggregate()
            .Match(mongoFilter);

        // Apply grouping using expression and convert to BsonDocument
        var groupedAggregate = aggregate.Group(groupByField, g => new
        {
            _id = g.Key,
            Count = g.Count(),
            Items = g.ToList()
        }).As<BsonDocument>();

        // Create pipeline for grouped results with sorting and pagination
        PipelineDefinition<BsonDocument, BsonDocument> docsFace = new EmptyPipelineDefinition<BsonDocument>();

        int? skip = request.Skip;
        if (skip.HasValue)
        {
            docsFace = docsFace.Skip(skip.Value);
        }

        int? take = request.Take ?? AggregatesQueryValidator<TAggregatesQuery, TWhere>.MaxItems;
        docsFace = docsFace.Limit(take.Value);

        const string docs = "Docs";
        const string total = "Total";

        IDictionary<string, BsonDocument[]> facet = await groupedAggregate
            .Facet<BsonDocument, IDictionary<string, BsonDocument[]>>(
                AggregateFacet.Create(docs, docsFace),
                AggregateFacet.Create(total, new EmptyPipelineDefinition<BsonDocument>().Count())
            ).FirstOrDefaultAsync(cancellationToken);

        BsonDocument[] totalResultSet = facet[total];
        int totalCount = totalResultSet.Length != 0 ? totalResultSet[0]["count"].AsInt32 : 0;

        IEnumerable<BsonDocument> groupedResults = facet[docs];
        return new PagedResult<TGroupDto>
        {
            Items = mapper.Map<IEnumerable<TGroupDto>>(groupedResults),
            TotalCount = totalCount
        };
    }
}