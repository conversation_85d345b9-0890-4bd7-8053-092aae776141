using AutoMapper;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;

namespace CoverGo.Payments.Application.Common.Handlers;

public abstract class AggregatesQueryHandler<TAggregate, TAggregatesQuery, TWhere>(
    IMapper mapper,
    IMongoCollection<TAggregate> mongoCollection)
    where TAggregate : AggregateRootBase<string>
    where TAggregatesQuery : PagedQuery<TWhere>
{
    protected static readonly FilterDefinitionBuilder<TAggregate> SBuilder = new();

    protected abstract FilterDefinition<TAggregate> GetFilter(TWhere? filter);

    protected virtual IEnumerable<SortDefinition<TAggregate>> GetSorts(TAggregatesQuery query) =>
        [Builders<TAggregate>.Sort.Descending(o => o.EntityAuditInfo.CreatedAt)];

    protected virtual BsonDocument? GetGroupBy(TAggregatesQuery query) => null;

    protected async Task<PagedResult<TDto>> Handle<TDto>(TAggregatesQuery request, CancellationToken cancellationToken)
    {
        FilterDefinition<TAggregate> mongoFilter = GetFilter(request.Where);
        BsonDocument? groupByExpression = GetGroupBy(request);

        IAggregateFluent<TAggregate> aggregate = mongoCollection.Aggregate()
            .Match(mongoFilter);

        // Apply grouping if specified - handle as BsonDocument pipeline
        if (groupByExpression != null)
        {
            IAggregateFluent<BsonDocument> groupedAggregate = aggregate.Group(groupByExpression);

            // Create pipeline for grouped results with pagination
            PipelineDefinition<BsonDocument, BsonDocument> groupedDocsFace = new EmptyPipelineDefinition<BsonDocument>();

            int? groupedSkip = request.Skip;
            if (groupedSkip.HasValue)
            {
                groupedDocsFace = groupedDocsFace.Skip(groupedSkip.Value);
            }

            int? groupedTake = request.Take ?? AggregatesQueryValidator<TAggregatesQuery, TWhere>.MaxItems;
            groupedDocsFace = groupedDocsFace.Limit(groupedTake.Value);

            const string groupedDocs = "Docs";
            const string groupedTotal = "Total";
            IDictionary<string, BsonDocument[]> groupedFacet = await groupedAggregate
                .Facet<BsonDocument, IDictionary<string, BsonDocument[]>>(
                    AggregateFacet.Create(groupedDocs, groupedDocsFace),
                    AggregateFacet.Create(groupedTotal, new EmptyPipelineDefinition<BsonDocument>().Count())
                ).FirstOrDefaultAsync(cancellationToken);
            BsonDocument[] groupedTotalResultSet = groupedFacet[groupedTotal];
            int groupedTotalCount = groupedTotalResultSet.Length != 0 ? groupedTotalResultSet[0]["count"].AsInt32 : 0;
            IEnumerable<BsonDocument> groupedResults = groupedFacet[groupedDocs];
            return new PagedResult<TDto> { Items = mapper.Map<IEnumerable<TDto>>(groupedResults), TotalCount = groupedTotalCount };
        }

        // Apply existing sorting and pagination logic for non-grouped results
        IEnumerable<SortDefinition<TAggregate>> sortDefinitions = GetSorts(request);
        PipelineDefinition<TAggregate, TAggregate> docsFace = sortDefinitions.Aggregate(
            new EmptyPipelineDefinition<TAggregate>(),
            (PipelineDefinition<TAggregate, TAggregate> accumulator, SortDefinition<TAggregate> sort) =>
                accumulator.Sort(sort));

        int? skip = request.Skip;
        if (skip.HasValue)
        {
            docsFace = docsFace.Skip(skip.Value);
        }

        int? take = request.Take ?? AggregatesQueryValidator<TAggregatesQuery, TWhere>.MaxItems;
        docsFace = docsFace.Limit(take.Value);

        const string docs = "Docs";
        const string total = "Total";
        IDictionary<string, BsonDocument[]> facet = await aggregate
            .Facet<TAggregate, IDictionary<string, BsonDocument[]>>(
                AggregateFacet.Create(docs, docsFace),
                AggregateFacet.Create(total, new EmptyPipelineDefinition<TAggregate>().Count())
            ).FirstOrDefaultAsync(cancellationToken);
        BsonDocument[] totalResultSet = facet[total];
        int totalCount = totalResultSet.Length != 0 ? totalResultSet[0]["count"].AsInt32 : 0;
        IEnumerable<TAggregate> list = facet[docs].Select(doc => BsonSerializer.Deserialize<TAggregate>(doc));
        return new PagedResult<TDto> { Items = mapper.Map<IEnumerable<TDto>>(list), TotalCount = totalCount };
    }


}